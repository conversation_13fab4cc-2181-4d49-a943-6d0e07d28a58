{"name": "atma-e2e-load-testing", "version": "1.0.0", "description": "E2E and Load Testing for ATMA Backend", "main": "index.js", "scripts": {"test:e2e": "node e2e-test.js", "test:load": "node load-test.js", "test:all": "npm run test:e2e && npm run test:load"}, "dependencies": {"axios": "^1.6.0", "socket.io-client": "^4.7.2", "chalk": "^4.1.2", "cli-progress": "^3.12.0", "uuid": "^9.0.0"}, "devDependencies": {}, "keywords": ["testing", "e2e", "load-testing", "atma"], "author": "ATMA Team", "license": "MIT"}